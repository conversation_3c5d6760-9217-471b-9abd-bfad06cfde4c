2025-05-30 23:57:57,713 - INFO - 使用脚本目录作为应用路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-30 23:57:57,718 - WARNING - 主配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml 不存在，正在自动生成默认配置...
2025-05-30 23:57:57,719 - INFO - 正在生成默认主配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:57:57,720 - INFO - 将从 DEFAULT_CONFIG_TEXT 创建或覆盖配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:57:57,744 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:57:57,744 - INFO - 默认主配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:58:12,984 - INFO - API密钥验证成功
2025-05-30 23:58:13,011 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:58:13,012 - INFO - 已将加密的API密钥保存到配置文件
2025-05-30 23:58:13,012 - INFO - 已成功生成默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:58:13,034 - INFO - 🔧 日志系统已初始化，调试模式：关闭
2025-05-30 23:58:13,091 - WARNING - 模式配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml 不存在，正在生成默认配置...
2025-05-30 23:58:13,092 - INFO - 正在生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:58:13,150 - INFO - 将从 DEFAULT_MODE_CONFIG_TEXT 创建或覆盖模式配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:58:13,283 - INFO - 模式配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:58:13,284 - INFO - 默认模式配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:58:13,285 - INFO - 已成功生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:58:13,417 - INFO - 语言模式配置已加载。
2025-05-30 23:58:13,417 - INFO - 语言模式配置已加载。
2025-05-30 23:58:13,418 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-30 23:58:13,418 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-30 23:58:13,431 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\translation_cache.db
2025-05-30 23:58:13,443 - INFO - 控制台线程已启动，准备进入循环。
2025-05-30 23:58:13,443 - INFO - 控制台循环开始，准备显示菜单。
2025-05-30 23:58:13,444 - INFO - 菜单已显示，等待用户输入。
2025-05-30 23:58:13,544 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-30 23:58:14,184 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-30 23:58:14,385 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-30 23:58:14,386 - INFO - API服务正常
2025-05-30 23:58:17,363 - INFO - 用户输入: 0
2025-05-30 23:58:23,021 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:58:23,037 - DEBUG - 日志系统已更新：debug_mode=True, log_level=10
2025-05-30 23:58:23,037 - INFO - 🔧 调试模式已开启 - 将显示详细的翻译信息
2025-05-30 23:58:23,038 - INFO - 🧪 测试调试模式功能...
2025-05-30 23:58:23,038 - INFO - 【原文】
这是一个测试文本
2025-05-30 23:58:23,038 - INFO - 【翻译结果】
This is a test text
2025-05-30 23:58:23,038 - INFO - 【API请求JSON】
请求体: {"test": "data"}
2025-05-30 23:58:23,039 - INFO - 发给大模型的完整提示词: 这是测试提示词
2025-05-30 23:58:23,039 - INFO - 检测到三次空格，触发翻译
2025-05-30 23:58:23,039 - INFO - API翻译成功
2025-05-30 23:58:23,039 - INFO - 【缓存命中】使用缓存结果
2025-05-30 23:58:23,039 - INFO - 🧪 调试模式功能测试完成
2025-05-30 23:58:26,749 - INFO - 控制台循环开始，准备显示菜单。
2025-05-30 23:58:26,752 - INFO - 菜单已显示，等待用户输入。
2025-05-30 23:58:45,947 - INFO - 用户输入: q
2025-05-30 23:58:45,947 - INFO - 用户请求退出程序
2025-05-30 23:58:45,947 - INFO - 正在关闭翻译程序...
2025-05-30 23:58:45,947 - INFO - 正在关闭翻译器...
2025-05-30 23:58:45,948 - DEBUG - 重置API状态缓存
2025-05-30 23:58:45,948 - DEBUG - 翻译器资源已释放
